// Rewards Page JavaScript Logic
window.$REWARDS.onLifecycle({
  selector: `.content`,
  page: /^\/\w+\/pages\/rewards/,
  onMount: (_, __, kill) => {
    console.log("Rewards page mounted")

    // Base URL constant for easy future changes
    const BASE_URL = 'http://localhost:3003'

    // Load CSS file
    const cssLink = document.createElement('link')
    cssLink.rel = 'stylesheet'
    cssLink.href = '/rewards-page/1_content.css'
    document.head.appendChild(cssLink)

    // Replace coin stacks with PNG image
    const initializeRewardIcons = () => {
      const coinStacks = document.querySelectorAll('.coin-stack')
      coinStacks.forEach(coinStack => {
        // Create image element
        const coinImage = document.createElement('img')
        coinImage.src = `${BASE_URL}/coins.png`
        coinImage.alt = 'Reward Coins'
        coinImage.className = 'coin-image'
        coinImage.style.width = '100%'
        coinImage.style.height = '100%'
        coinImage.style.objectFit = 'contain'

        // Replace coin stack with image
        coinStack.innerHTML = ''
        coinStack.appendChild(coinImage)
      })
    }

    // Initialize icons after a short delay to ensure DOM is ready
    setTimeout(initializeRewardIcons, 100)

    // Initialize VIP container drag functionality
    const initVipDrag = () => {
      const container = document.getElementById('vipContainer')
      if (!container) return

      let isDown = false
      let startX

      container.addEventListener('mousedown', (e) => {
        isDown = true
        container.classList.add('dragging')
        startX = e.pageX
      })

      container.addEventListener('mouseleave', () => {
        isDown = false
        container.classList.remove('dragging')
      })

      container.addEventListener('mouseup', (e) => {
        if (!isDown) return
        isDown = false
        container.classList.remove('dragging')

        // Calculate drag distance and determine if we should change slides
        const endX = e.pageX
        const dragDistance = startX - endX
        const threshold = 100 // Minimum drag distance to trigger slide change

        if (Math.abs(dragDistance) > threshold) {
          const maxIndex = Math.max(0, totalCards - cardsPerView)
          if (dragDistance > 0 && currentSlideIndex < maxIndex) {
            // Dragged left - go to next slide
            currentSlideIndex++
          } else if (dragDistance < 0 && currentSlideIndex > 0) {
            // Dragged right - go to previous slide
            currentSlideIndex--
          }
          rafThrottledUpdateSliderPosition()
          rafThrottledUpdateArrowStates()
        }
      })

      container.addEventListener('mousemove', (e) => {
        if (!isDown) return
        e.preventDefault()
      })
    }

    // Initialize drag functionality after a short delay
    setTimeout(initVipDrag, 100)

    // Global functions for button interactions
    window.claimTrialBonus = (buttonElement) => {
      console.log('Claiming trial bonus...')

      // Find the button element if not passed directly
      if (!buttonElement) {
        buttonElement = document.querySelector('.welcome-bonus-btn')
      }

      // Prevent multiple claims
      if (buttonElement.disabled || buttonElement.classList.contains('claimed')) return

      // Simple button state change only
      buttonElement.textContent = 'Claimed'
      buttonElement.disabled = true
      buttonElement.classList.add('claimed')

      // Apply persistent claimed state to banner without animations
      const banner = buttonElement.closest('.welcome-bonus-banner')
      if (banner) {
        banner.classList.add('bonus-claimed-state')
      }

      console.log('Welcome bonus claimed successfully!')
    }

    window.claimReward = (type, buttonElement) => {
      console.log(`Claiming ${type} reward...`)

      // Find the button element if not passed directly
      if (!buttonElement) {
        buttonElement = document.querySelector(`[onclick*="${type}"]`)
      }

      // Prevent multiple claims
      if (buttonElement.disabled) return

      // Simple button state change only
      buttonElement.textContent = 'Claimed'
      buttonElement.disabled = true
      buttonElement.classList.add('claimed')

      console.log(`${type.charAt(0).toUpperCase() + type.slice(1)} reward claimed successfully!`)
    }

    window.applyPromoCode = () => {
      const promoInput = document.getElementById('promoCode')
      const messageDiv = document.getElementById('promoMessage')

      if (!promoInput || !messageDiv) return

      const code = promoInput.value.trim().toUpperCase()

      if (!code) {
        messageDiv.textContent = 'Please enter a promo code'
        messageDiv.className = 'promo-message error'
        return
      }

      // Simulate promo code validation
      const validCodes = ['WELCOME100', 'BONUS50', 'VIP200']

      if (validCodes.includes(code)) {
        messageDiv.textContent = `Promo code "${code}" applied successfully!`
        messageDiv.className = 'promo-message success'
        promoInput.value = ''
      } else {
        messageDiv.textContent = 'Invalid promo code. Please try again.'
        messageDiv.className = 'promo-message error'
      }
    }

    // VIP Slider functionality
    let currentSlideIndex = 0
    let cardsPerView = 3
    let totalCards = 0

    // RAF throttle function for performance optimization
    function rafThrottle(fn) {
      let locked = false
      return (...args) => {
        if (locked) return
        locked = true
        requestAnimationFrame(() => {
          fn(...args)
          locked = false
        })
      }
    }



    const initializeVipSlider = () => {
      const container = document.getElementById('vipContainer')
      const grid = container?.querySelector('.vip-ranks-grid')
      const leftArrow = document.getElementById('vipNavLeft')
      const rightArrow = document.getElementById('vipNavRight')

      if (!grid || !leftArrow || !rightArrow) return

      totalCards = grid.children.length
      updateCardsPerView()
      rafThrottledUpdateSliderPosition()
      rafThrottledUpdateArrowStates()

      // Arrow click handlers
      leftArrow.addEventListener('click', () => {
        if (currentSlideIndex > 0) {
          currentSlideIndex--
          rafThrottledUpdateSliderPosition()
          rafThrottledUpdateArrowStates()
        }
      })

      rightArrow.addEventListener('click', () => {
        const maxIndex = Math.max(0, totalCards - cardsPerView)
        if (currentSlideIndex < maxIndex) {
          currentSlideIndex++
          rafThrottledUpdateSliderPosition()
          rafThrottledUpdateArrowStates()
        }
      })

      // Touch/swipe support
      let startX = 0
      let isDragging = false

      container.addEventListener('touchstart', (e) => {
        startX = e.touches[0].clientX
        isDragging = true
      })

      container.addEventListener('touchmove', (e) => {
        if (!isDragging) return
        e.preventDefault()
      })

      container.addEventListener('touchend', (e) => {
        if (!isDragging) return
        isDragging = false

        const endX = e.changedTouches[0].clientX
        const diff = startX - endX

        if (Math.abs(diff) > 50) { // Minimum swipe distance
          if (diff > 0) {
            // Swipe left - next
            rightArrow.click()
          } else {
            // Swipe right - previous
            leftArrow.click()
          }
        }
      })

      // Resize handler
      window.addEventListener('resize', () => {
        updateCardsPerView()
        currentSlideIndex = Math.min(currentSlideIndex, Math.max(0, totalCards - cardsPerView))
        rafThrottledUpdateSliderPosition()
        rafThrottledUpdateArrowStates()
      })

      // Add scroll event listener to synchronize arrow states with manual scrolling
      container.addEventListener('scroll', () => {
        // Calculate current slide index based on scroll position
        const cardWidth = 180 + 24 // Fixed card width + gap
        const scrollPosition = Math.abs(container.scrollLeft)
        const calculatedIndex = Math.round(scrollPosition / cardWidth)

        // Update current slide index if it changed
        if (calculatedIndex !== currentSlideIndex) {
          currentSlideIndex = Math.min(calculatedIndex, Math.max(0, totalCards - cardsPerView))
          rafThrottledUpdateArrowStates()
        }
      })
    }

    const updateCardsPerView = () => {
      const width = window.innerWidth
      const container = document.querySelector('.vip-ranks-container')

      if (width < 768) {
        cardsPerView = 2
        if (container) container.style.width = '384px' // Mobile: (180 × 2) + (24 × 1) = 384px
      } else if (width < 1200) {
        cardsPerView = 4
        if (container) container.style.width = '768px' // Tablet: (180 × 4) + (24 × 3) = 768px
      } else {
        cardsPerView = 6
        if (container) container.style.width = '1152px' // Desktop: (180 × 6) + (24 × 5) = 1152px
      }
    }

    const updateSliderPosition = () => {
      const grid = document.querySelector('.vip-ranks-grid')
      if (!grid) return

      // Fixed card width and gap for precise calculation
      const cardWidth = 180
      const gap = 24
      const cardWithGap = cardWidth + gap

      const translateX = -currentSlideIndex * cardWithGap
      grid.style.transform = `translateX(${translateX}px)`
    }

    const updateArrowStates = () => {
      const leftArrow = document.getElementById('vipNavLeft')
      const rightArrow = document.getElementById('vipNavRight')

      if (leftArrow) {
        leftArrow.disabled = currentSlideIndex === 0
      }

      if (rightArrow) {
        const maxIndex = Math.max(0, totalCards - cardsPerView)
        rightArrow.disabled = currentSlideIndex >= maxIndex
      }
    }

    // Create throttled versions of functions
    const rafThrottledUpdateSliderPosition = rafThrottle(updateSliderPosition)
    const rafThrottledUpdateArrowStates = rafThrottle(updateArrowStates)

    // VIP Perks data
    const vipPerksData = {
      'BRONZE': {
        name: 'Bronze VIP',
        perks: [
          'Welcome bonus boost',
          'Basic customer support',
          'Monthly cashback',
          'Birthday bonus'
        ]
      },
      'SILVER': {
        name: 'Silver VIP',
        perks: [
          'Enhanced welcome bonus',
          'Priority customer support',
          'Weekly cashback',
          'Birthday bonus',
          'Exclusive tournaments'
        ]
      },
      'GOLD': {
        name: 'Gold VIP',
        perks: [
          'Premium welcome bonus',
          'VIP customer support',
          'Daily cashback',
          'Birthday & anniversary bonus',
          'VIP tournaments',
          'Faster withdrawals'
        ]
      },
      'PLATINUM': {
        name: 'Platinum VIP',
        perks: [
          'Elite welcome bonus',
          'Dedicated account manager',
          'Enhanced daily cashback',
          'Special occasion bonuses',
          'Exclusive VIP events',
          'Priority withdrawals',
          'Personal promotions'
        ]
      },
      'DIAMOND': {
        name: 'Diamond VIP',
        perks: [
          'Ultimate welcome bonus',
          'Personal VIP manager',
          'Maximum cashback rates',
          'Luxury gifts & bonuses',
          'Private VIP events',
          'Instant withdrawals',
          'Custom promotions',
          'VIP travel packages'
        ]
      },
      'ELITE': {
        name: 'Elite VIP',
        perks: [
          'Exclusive elite bonuses',
          'Elite concierge service',
          'Premium cashback rates',
          'Luxury lifestyle rewards',
          'Elite-only events',
          'No withdrawal limits',
          'Bespoke promotions',
          'Elite travel experiences',
          'Personal gaming advisor'
        ]
      },
      'MACRO BLACK': {
        name: 'Macro Black VIP',
        perks: [
          'Ultimate elite bonuses',
          'Black card concierge',
          'Unlimited cashback',
          'Exclusive luxury rewards',
          'Private gaming events',
          'Unlimited withdrawals',
          'Personalized everything',
          'World-class experiences',
          'Dedicated gaming team',
          'Invitation-only privileges'
        ]
      }
    }

    window.viewPerks = (tier) => {
      console.log(`Viewing perks for ${tier}...`)
      showVipModal(tier)
    }

    const showVipModal = (tier) => {
      const modal = document.getElementById('vipModal')
      const title = document.getElementById('vipModalTitle')
      const body = document.getElementById('vipModalBody')

      if (!modal || !title || !body) return

      const tierData = vipPerksData[tier.toUpperCase()]
      if (!tierData) return

      // Set modal content
      title.textContent = `${tierData.name} Benefits`

      body.innerHTML = `
        <div class="vip-modal-tier-name">${tierData.name}</div>
        <ul class="vip-modal-perks-list">
          ${tierData.perks.map(perk => `
            <li class="vip-modal-perk">
              <svg class="vip-modal-perk-icon" viewBox="0 0 24 24" fill="none">
                <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>${perk}</span>
            </li>
          `).join('')}
        </ul>
      `

      // Show modal
      modal.classList.add('active')
      document.body.style.overflow = 'hidden'

      // Focus management for accessibility
      const closeButton = document.getElementById('vipModalClose')
      if (closeButton) {
        closeButton.focus()
      }
    }

    const hideVipModal = () => {
      const modal = document.getElementById('vipModal')
      if (modal) {
        modal.classList.remove('active')
        document.body.style.overflow = ''
      }
    }

    // Modal event listeners
    const initializeVipModal = () => {
      const modal = document.getElementById('vipModal')
      const backdrop = document.getElementById('vipModalBackdrop')
      const closeButton = document.getElementById('vipModalClose')

      if (closeButton) {
        closeButton.addEventListener('click', hideVipModal)
      }

      if (backdrop) {
        backdrop.addEventListener('click', hideVipModal)
      }

      // Escape key to close
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && modal?.classList.contains('active')) {
          hideVipModal()
        }
      })
    }

    // Initialize everything after DOM is ready
    setTimeout(() => {
      initializeVipSlider()
      initializeVipModal()
    }, 500)

    // Cleanup function
    kill(() => {
      // Remove global functions
      delete window.claimTrialBonus
      delete window.claimReward
      delete window.applyPromoCode
      delete window.viewPerks

      // Remove CSS link
      const cssLink = document.querySelector('link[href="/rewards-page/1_content.css"]')
      if (cssLink) {
        cssLink.remove()
      }
    })
  }
})